from flask import Flask, render_template, request, redirect, url_for, jsonify
from flask_cors import CORS
import pandas as pd
import os
import sys
import shutil
import json
from datetime import datetime
import requests
import urllib.parse
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

app = Flask(__name__)
CORS(app, origins="*", methods=["GET", "POST", "OPTIONS"], allow_headers=["Content-Type"])  # Enable CORS for all routes

# --------- PyInstaller Compatibility ---------
def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.abspath("."), relative_path)

BUNDLED_DATA_FILE = resource_path('data.xlsx')
APP_FOLDER = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))
DATA_FILE = os.path.join(APP_FOLDER, 'data.xlsx')

if not os.path.exists(DATA_FILE):
    shutil.copy(BUNDLED_DATA_FILE, DATA_FILE)

# --------- Helpers ---------
def load_data():
    if os.path.exists(DATA_FILE):
        df = pd.read_excel(DATA_FILE)
        df.fillna('', inplace=True)
        data = df.to_dict(orient='records')

        # Ensure all items have access_count field for backward compatibility
        for item in data:
            if 'access_count' not in item or pd.isna(item['access_count']):
                item['access_count'] = 0

        return data
    return []

def save_data(data):
    df = pd.DataFrame(data)
    df.to_excel(DATA_FILE, index=False)



# --------- Routes ---------
@app.route('/')
def index():
    data = load_data()
    print(f"DEBUG: Loaded {len(data)} items from data file")  # Debug log
    search = request.args.get('search', '').lower()
    tag_filter = request.args.get('tag', '').lower()
    print(f"DEBUG: Search='{search}', Tag='{tag_filter}'")  # Debug log

    # 🔍 Search and tag filter BEFORE pagination
    if search:
        import re

        def parse_search_query(search_input):
            """Parse search query with AND, OR, NOT operators"""
            search_input = search_input.strip()

            # Handle quoted phrases first
            quoted_pattern = r'"([^"]*)"'
            quoted_matches = re.findall(quoted_pattern, search_input)
            quoted_placeholders = {}

            # Replace quoted phrases with placeholders
            for i, quoted_term in enumerate(quoted_matches):
                placeholder = f"__QUOTED_{i}__"
                quoted_placeholders[placeholder] = quoted_term.strip()
                search_input = search_input.replace(f'"{quoted_term}"', placeholder, 1)

            # Parse NOT conditions first (highest precedence)
            not_terms = []
            not_pattern = r'\bnot\s+(\S+)'
            not_matches = re.findall(not_pattern, search_input, re.IGNORECASE)
            for not_term in not_matches:
                # Replace placeholder back to original quoted term if needed
                if not_term in quoted_placeholders:
                    not_terms.append(quoted_placeholders[not_term])
                else:
                    not_terms.append(not_term)

            # Remove NOT conditions from search string
            search_input = re.sub(r'\bnot\s+\S+', '', search_input, flags=re.IGNORECASE).strip()

            # Parse AND conditions (medium precedence)
            and_groups = []
            and_pattern = r'\band\s+'
            and_parts = re.split(and_pattern, search_input, flags=re.IGNORECASE)

            if len(and_parts) > 1:
                # We have AND conditions
                for part in and_parts:
                    part = part.strip()
                    if part:
                        # Handle OR within AND groups
                        or_terms = []
                        or_parts = re.split(r'\bor\s+', part, flags=re.IGNORECASE)
                        for or_part in or_parts:
                            or_part = or_part.strip()
                            if or_part:
                                # Replace placeholder back to original quoted term if needed
                                if or_part in quoted_placeholders:
                                    or_terms.append(quoted_placeholders[or_part])
                                else:
                                    # Split by spaces and commas for individual terms
                                    terms = [term.strip() for term in or_part.replace(',', ' ').split() if term.strip()]
                                    or_terms.extend(terms)
                        if or_terms:
                            and_groups.append(or_terms)
            else:
                # No AND conditions, parse OR conditions (lowest precedence)
                or_terms = []
                or_parts = re.split(r'\bor\s+', search_input, flags=re.IGNORECASE)
                for or_part in or_parts:
                    or_part = or_part.strip()
                    if or_part:
                        # Replace placeholder back to original quoted term if needed
                        if or_part in quoted_placeholders:
                            or_terms.append(quoted_placeholders[or_part])
                        else:
                            # Split by spaces and commas for individual terms
                            terms = [term.strip() for term in or_part.replace(',', ' ').split() if term.strip()]
                            or_terms.extend(terms)

                if or_terms:
                    and_groups = [or_terms]  # Single OR group

            return and_groups, not_terms

        def term_matches(term, item_text):
            """Check if a term matches in the item text"""
            term_lower = term.lower()
            # For single words, use word boundaries to avoid partial matches
            if ' ' not in term_lower:
                return bool(re.search(r'\b' + re.escape(term_lower) + r'\b', item_text))
            else:
                # For phrases, use exact substring matching
                return term_lower in item_text

        def matches_search(item):
            """Check if item matches the search criteria"""
            item_text = (str(item['title']) + ' ' + str(item['url']) + ' ' + str(item.get('tags', ''))).lower()

            and_groups, not_terms = parse_search_query(search)

            # Check NOT conditions first (if any NOT term matches, exclude the item)
            for not_term in not_terms:
                if term_matches(not_term, item_text):
                    return False

            # If no AND groups, return True (no positive conditions to check)
            if not and_groups:
                return True

            # Check AND conditions (all groups must have at least one match)
            for or_group in and_groups:
                group_matched = False
                for term in or_group:
                    if term_matches(term, item_text):
                        group_matched = True
                        break

                # If any AND group doesn't match, the item doesn't match
                if not group_matched:
                    return False

            return True

        data = [item for item in data if matches_search(item)]
    if tag_filter:
        data = [item for item in data if tag_filter in str(item.get('tags', '')).lower()]

    # 📊 Sort by access_count (most accessed first)
    data.sort(key=lambda x: x.get('access_count', 0), reverse=True)

    print(f"DEBUG: Rendering template with {len(data)} items")  # Debug log
    return render_template('index.html', items=data, search=search,
                           selected_tag=tag_filter)

@app.route('/upload', methods=['POST'])
def upload():
    file = request.files['file']
    if file:
        file.save(DATA_FILE)
    return redirect(url_for('index'))

@app.route('/add', methods=['GET', 'POST'])
def add():
    if request.method == 'GET':
        # Handle bookmarklet popup mode
        title = request.args.get('title', '')
        url = request.args.get('url', '')
        popup = request.args.get('popup', '')

        if popup:
            # Return a simple popup page for bookmarklet
            return f'''
            <!DOCTYPE html>
            <html>
            <head>
                <title>Save to Pocket</title>
                <style>
                    body {{ font-family: Arial, sans-serif; padding: 20px; }}
                    .form-group {{ margin: 10px 0; }}
                    input[type="text"], input[type="url"] {{ width: 100%; padding: 8px; }}
                    button {{ padding: 10px 20px; margin: 5px; }}
                    .success {{ color: green; }}
                    .error {{ color: red; }}
                </style>
            </head>
            <body>
                <h2>Save to Pocket</h2>
                <form method="POST" action="/add">
                    <div class="form-group">
                        <label>Title:</label>
                        <input type="text" name="title" value="{title}" required>
                    </div>
                    <div class="form-group">
                        <label>URL:</label>
                        <input type="url" name="url" value="{url}" required>
                    </div>
                    <div class="form-group">
                        <label>Tags:</label>
                        <input type="text" name="tags" placeholder="comma-separated">
                    </div>
                    <input type="hidden" name="popup" value="1">
                    <button type="submit">Save Bookmark</button>
                    <button type="button" onclick="window.close()">Cancel</button>
                </form>
            </body>
            </html>
            '''

    # Handle POST request (form submission)
    data = load_data()
    new_item = {
        'title': request.form['title'],
        'url': request.form['url'],
        'tags': request.form.get('tags', ''),
        'time_added': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'status': 'unread',
        'access_count': 0
    }
    data.append(new_item)
    save_data(data)

    # If it's a popup, show success message and close
    if request.form.get('popup'):
        return '''
        <!DOCTYPE html>
        <html>
        <head><title>Bookmark Saved</title></head>
        <body>
            <h2 style="color: green;">✅ Bookmark Saved Successfully!</h2>
            <p>You can close this window now.</p>
            <script>
                setTimeout(function() {
                    window.close();
                }, 2000);
            </script>
        </body>
        </html>
        '''

    return redirect(url_for('index'))

@app.route('/api/add-bookmark', methods=['POST', 'OPTIONS'])
def api_add_bookmark():
    """API endpoint for adding bookmarks from external sources (bookmarklet, extension, etc.)"""
    # Handle CORS preflight requests
    if request.method == 'OPTIONS':
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
        response.headers.add('Access-Control-Allow-Methods', 'POST, OPTIONS')
        return response

    try:
        # Handle both JSON and form data
        if request.is_json:
            data_source = request.get_json()
        else:
            data_source = request.form

        title = data_source.get('title', '').strip()
        url = data_source.get('url', '').strip()
        tags = data_source.get('tags', '').strip()

        if not url:
            response = jsonify({'success': False, 'error': 'URL is required'})
            response.headers.add('Access-Control-Allow-Origin', '*')
            return response, 400

        if not title:
            title = url  # Use URL as title if no title provided

        data = load_data()

        # Check if URL already exists
        existing_item = next((item for item in data if item['url'] == url), None)
        if existing_item:
            response = jsonify({
                'success': False,
                'error': 'URL already exists in your bookmarks',
                'existing_title': existing_item['title']
            })
            response.headers.add('Access-Control-Allow-Origin', '*')
            return response, 409

        new_item = {
            'title': title,
            'url': url,
            'tags': tags,
            'time_added': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'status': 'unread',
            'access_count': 0
        }
        data.append(new_item)
        save_data(data)

        print(f"DEBUG: Added bookmark via API - Title: '{title}', URL: '{url}'")

        response = jsonify({
            'success': True,
            'message': 'Bookmark added successfully',
            'bookmark': new_item
        })
        response.headers.add('Access-Control-Allow-Origin', '*')
        return response

    except Exception as e:
        print(f"DEBUG: Error adding bookmark via API: {str(e)}")
        response = jsonify({'success': False, 'error': str(e)})
        response.headers.add('Access-Control-Allow-Origin', '*')
        return response, 500

@app.route('/delete', methods=['POST'])
def delete():
    title = request.form['title']
    url = request.form['url']
    search = request.form.get('search', '')
    tag = request.form.get('tag', '')

    data = load_data()
    data = [item for item in data if not (item['title'] == title and item['url'] == url)]
    save_data(data)

    # Preserve search and tag filters in redirect
    redirect_params = {}
    if search:
        redirect_params['search'] = search
    if tag:
        redirect_params['tag'] = tag

    return redirect(url_for('index', **redirect_params))

@app.route('/api/items')
def api_items():
    data = load_data()
    return jsonify(data)



@app.route('/api/access-url', methods=['POST'])
def api_access_url():
    """API endpoint to track URL access"""
    try:
        url = request.json.get('url')
        if not url:
            return jsonify({'success': False, 'error': 'URL is required'}), 400

        data = load_data()
        updated = False

        for item in data:
            if item['url'] == url:
                item['access_count'] = item.get('access_count', 0) + 1
                updated = True
                break

        if updated:
            save_data(data)
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'URL not found'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/update-item', methods=['POST'])
def api_update_item():
    """API endpoint to update item title or tags"""
    try:
        url = request.json.get('url')
        field = request.json.get('field')  # 'title' or 'tags'
        value = request.json.get('value')

        if not url or not field:
            return jsonify({'success': False, 'error': 'URL and field are required'}), 400

        if field not in ['title', 'tags', 'url']:
            return jsonify({'success': False, 'error': 'Field must be title, tags, or url'}), 400

        data = load_data()
        updated = False

        for item in data:
            if item['url'] == url:
                if field == 'url':
                    # Special handling for URL updates
                    if not value or not value.strip():
                        return jsonify({'success': False, 'error': 'URL cannot be empty'}), 400
                    print(f"DEBUG: Updating URL from '{url}' to '{value.strip()}'")  # Debug log
                    item['url'] = value.strip()
                else:
                    print(f"DEBUG: Updating {field} for URL '{url}' to '{value}'")  # Debug log
                    item[field] = value or ''
                updated = True
                break

        if updated:
            save_data(data)
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'URL not found'}), 404

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/check-duplicates')
def api_check_duplicates():
    """API endpoint to check for duplicate URLs"""
    try:
        data = load_data()
        url_groups = {}

        # Group items by URL
        for item in data:
            url = item.get('url', '').strip().lower()
            if url:
                if url not in url_groups:
                    url_groups[url] = []
                url_groups[url].append(item)

        # Find duplicates (groups with more than one item)
        duplicates = []
        for url, items in url_groups.items():
            if len(items) > 1:
                duplicates.append({
                    'url': url,
                    'count': len(items),
                    'items': items
                })

        # Sort by count (most duplicates first)
        duplicates.sort(key=lambda x: x['count'], reverse=True)

        return jsonify({
            'success': True,
            'duplicates': duplicates,
            'total_duplicates': len(duplicates),
            'total_duplicate_items': sum(d['count'] for d in duplicates)
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Quick search buttons data file
QUICK_SEARCH_FILE = os.path.join(APP_FOLDER, 'quick_searches.json')

def load_quick_searches():
    """Load quick search button configurations"""
    try:
        if os.path.exists(QUICK_SEARCH_FILE):
            import json
            with open(QUICK_SEARCH_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception:
        pass

    # Return default empty buttons
    return [{'label': '', 'search': ''} for _ in range(8)]

def save_quick_searches(quick_searches):
    """Save quick search button configurations"""
    try:
        import json
        with open(QUICK_SEARCH_FILE, 'w', encoding='utf-8') as f:
            json.dump(quick_searches, f, ensure_ascii=False, indent=2)
        return True
    except Exception:
        return False

@app.route('/api/quick-searches')
def api_get_quick_searches():
    """API endpoint to get quick search buttons"""
    try:
        quick_searches = load_quick_searches()
        return jsonify({'success': True, 'quick_searches': quick_searches})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/quick-searches', methods=['POST'])
def api_save_quick_searches():
    """API endpoint to save quick search buttons"""
    try:
        quick_searches = request.json.get('quick_searches', [])

        # Validate data
        if not isinstance(quick_searches, list) or len(quick_searches) != 8:
            return jsonify({'success': False, 'error': 'Invalid quick searches data'}), 400

        # Ensure each item has required fields
        for i, item in enumerate(quick_searches):
            if not isinstance(item, dict):
                quick_searches[i] = {'label': '', 'search': ''}
            else:
                quick_searches[i] = {
                    'label': str(item.get('label', '')),
                    'search': str(item.get('search', ''))
                }

        if save_quick_searches(quick_searches):
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'error': 'Failed to save quick searches'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

def check_url_validity(url, timeout=10):
    """Check if a URL is valid and accessible - only reports critical errors"""
    try:
        # Basic URL format validation
        parsed = urllib.parse.urlparse(url)
        if not parsed.scheme or not parsed.netloc:
            return True, "OK (skipped - invalid format)"  # Don't report format issues

        # Make HTTP request with timeout
        response = requests.head(url, timeout=timeout, allow_redirects=True)

        # Only report specific critical errors
        if response.status_code == 404:
            return False, "HTTP 404 (Not Found)"
        elif response.status_code < 400 or response.status_code >= 500:
            # Accept all 2xx, 3xx responses and ignore 5xx server errors
            return True, f"OK ({response.status_code})"
        else:
            # Ignore other 4xx errors (403, 401, etc.) - they might be access restrictions
            return True, f"OK ({response.status_code})"

    except requests.exceptions.Timeout:
        return False, "Timeout"
    except requests.exceptions.ConnectionError:
        return False, "Connection failed"
    except requests.exceptions.TooManyRedirects:
        return True, "OK (redirects)"  # Don't report redirect loops as critical
    except requests.exceptions.RequestException:
        return True, "OK (request error)"  # Don't report other request errors
    except Exception:
        return True, "OK (error)"  # Don't report other errors

def validate_urls_batch(urls, max_workers=10):
    """Validate multiple URLs concurrently"""
    results = []

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all URL checks
        future_to_url = {
            executor.submit(check_url_validity, url): url
            for url in urls
        }

        # Collect results as they complete
        for future in as_completed(future_to_url):
            url = future_to_url[future]
            try:
                is_valid, message = future.result()
                results.append({
                    'url': url,
                    'is_valid': is_valid,
                    'message': message
                })
            except Exception as e:
                results.append({
                    'url': url,
                    'is_valid': False,
                    'message': f"Validation error: {str(e)}"
                })

    return results

@app.route('/api/validate-urls', methods=['POST'])
def validate_urls():
    """API endpoint to validate all URLs"""
    try:
        data = load_data()
        urls = [item['url'] for item in data if item.get('url')]

        print(f"DEBUG: Starting validation of {len(urls)} URLs")

        # Validate URLs in batches with progress tracking
        validation_results = validate_urls_with_progress(urls)

        # Find problematic URLs
        problems = []
        for result in validation_results:
            if not result['is_valid']:
                # Find the full item data for this URL
                item_data = next((item for item in data if item['url'] == result['url']), None)
                if item_data:
                    problems.append({
                        'url': result['url'],
                        'title': item_data.get('title', 'No title'),
                        'tags': item_data.get('tags', ''),
                        'access_count': item_data.get('access_count', 0),
                        'error': result['message']
                    })

        print(f"DEBUG: Found {len(problems)} problematic URLs")

        return jsonify({
            'success': True,
            'total_checked': len(urls),
            'problems_found': len(problems),
            'problems': problems
        })

    except Exception as e:
        print(f"DEBUG: Error during URL validation: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def validate_urls_with_progress(urls, max_workers=10):
    """Validate multiple URLs concurrently with progress tracking"""
    results = []
    completed = 0
    total = len(urls)

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all URL checks
        future_to_url = {
            executor.submit(check_url_validity, url): url
            for url in urls
        }

        # Collect results as they complete
        for future in as_completed(future_to_url):
            url = future_to_url[future]
            try:
                is_valid, message = future.result()
                results.append({
                    'url': url,
                    'is_valid': is_valid,
                    'message': message
                })
            except Exception as e:
                results.append({
                    'url': url,
                    'is_valid': False,
                    'message': f"Validation error: {str(e)}"
                })

            completed += 1
            progress = (completed / total) * 100
            print(f"DEBUG: Validation progress: {completed}/{total} ({progress:.1f}%)")

    return results

@app.route('/api/delete-invalid-urls', methods=['POST'])
def delete_invalid_urls():
    """API endpoint to delete specific invalid URLs"""
    try:
        request_data = request.get_json()
        urls_to_delete = request_data.get('urls', [])

        if not urls_to_delete:
            return jsonify({'success': False, 'error': 'No URLs provided'}), 400

        data = load_data()
        original_count = len(data)

        # Remove items with the specified URLs
        data = [item for item in data if item['url'] not in urls_to_delete]

        deleted_count = original_count - len(data)
        save_data(data)

        print(f"DEBUG: Deleted {deleted_count} invalid URLs")

        return jsonify({
            'success': True,
            'deleted_count': deleted_count
        })

    except Exception as e:
        print(f"DEBUG: Error deleting invalid URLs: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/favicon.ico')
def favicon():
    """Simple favicon endpoint to avoid 404 errors"""
    return '', 204

@app.route('/test-bookmarklet')
def test_bookmarklet():
    """Test page for bookmarklet functionality"""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Bookmarklet Test Page</title>
    </head>
    <body>
        <h1>Test Page for Bookmarklet</h1>
        <p>This is a test page to verify bookmarklet functionality.</p>
        <p>Use this page to test your bookmarklet by clicking it in your bookmarks bar.</p>
        <script>
            console.log('Test page loaded successfully');
        </script>
    </body>
    </html>
    '''

@app.route('/debug')
def debug():
    """Debug route with limited items to test JavaScript issues"""
    data = load_data()
    # Only show first 5 items to debug
    limited_data = data[:5]

    search = request.args.get('search', '')
    selected_tag = request.args.get('tag', '')

    print(f"DEBUG: Rendering debug page with {len(limited_data)} items")

    return render_template('index.html',
                         items=limited_data,
                         search=search,
                         selected_tag=selected_tag)

@app.route('/get-bookmarklet-code')
def get_bookmarklet_code():
    """Return just the bookmarklet code for testing"""
    server_url = request.url_root.rstrip('/')

    bookmarklet_code = f"""javascript:(function(){{
        try {{
            var title = document.title || document.URL;
            var url = document.URL;
            var serverUrl = {json.dumps(server_url)};

            alert('🚀 Bookmarklet started!\\\\n\\\\nTitle: ' + title + '\\\\nURL: ' + url + '\\\\nServer: ' + serverUrl + '\\\\n\\\\nClick OK to continue...');

            console.log('Bookmarklet starting - Title:', title, 'URL:', url, 'Server:', serverUrl);

            fetch(serverUrl + '/api/add-bookmark', {{
                method: 'POST',
                headers: {{
                    'Content-Type': 'application/json',
                }},
                body: JSON.stringify({{
                    title: title,
                    url: url
                }})
            }})
            .then(function(response) {{
                console.log('Response received - Status:', response.status, 'OK:', response.ok);
                return response.text().then(function(text) {{
                    console.log('Raw response text:', text);
                    try {{
                        var data = JSON.parse(text);
                        return {{ status: response.status, data: data, ok: response.ok }};
                    }} catch (e) {{
                        console.error('JSON parse error:', e);
                        return {{ status: response.status, data: {{ error: 'Invalid JSON response: ' + text }}, ok: response.ok }};
                    }}
                }});
            }})
            .then(function(result) {{
                console.log('Processed result:', result);
                if (result.ok && result.data.success) {{
                    alert('✅ SUCCESS! Bookmark saved!\\\\n\\\\nTitle: ' + title + '\\\\nURL: ' + url + '\\\\nServer: ' + serverUrl);
                }} else if (result.status === 409) {{
                    alert('⚠️ DUPLICATE! This URL is already bookmarked!\\\\n\\\\nExisting title: ' + (result.data.existing_title || 'Unknown') + '\\\\nURL: ' + url);
                }} else {{
                    alert('❌ ERROR! Failed to save bookmark\\\\n\\\\nStatus: ' + result.status + '\\\\nError: ' + (result.data.error || 'Unknown error') + '\\\\nServer: ' + serverUrl);
                }}
            }})
            .catch(function(error) {{
                console.error('Fetch error:', error);
                alert('❌ CONNECTION ERROR!\\\\n\\\\nServer: ' + serverUrl + '\\\\nError: ' + error.message + '\\\\n\\\\nCheck console for details');
            }});
        }} catch (e) {{
            console.error('Bookmarklet script error:', e);
            alert('❌ SCRIPT ERROR!\\\\n\\\\nError: ' + e.message + '\\\\n\\\\nCheck console for details');
        }}
    }})();"""

    return jsonify({
        'bookmarklet': bookmarklet_code,
        'server_url': server_url
    })

@app.route('/test-bookmarklet-generation')
def test_bookmarklet_generation():
    """Test bookmarklet generation"""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Bookmarklet Generation Test</title>
    </head>
    <body>
        <h1>Bookmarklet Generation Test</h1>
        <button onclick="generateAndTestBookmarklet()">Generate & Test Bookmarklet</button>
        <div id="result"></div>
        <textarea id="bookmarkletCode" style="width: 100%; height: 200px; margin-top: 20px;"></textarea>

        <script>
            function generateAndTestBookmarklet() {
                try {
                    const serverUrl = window.location.origin.replace(/\\\\/g, '/');
                    console.log('Server URL:', serverUrl);

                    const bookmarkletCode = `javascript:(function(){
                        try {
                            var title = document.title || document.URL;
                            var url = document.URL;
                            var serverUrl = ${JSON.stringify(serverUrl)};

                            alert('🚀 Bookmarklet started!\\\\n\\\\nTitle: ' + title + '\\\\nURL: ' + url + '\\\\nServer: ' + serverUrl + '\\\\n\\\\nClick OK to continue...');

                            fetch(serverUrl + '/api/add-bookmark', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    title: title + ' (Test)',
                                    url: url + '#test'
                                })
                            })
                            .then(function(response) {
                                return response.json();
                            })
                            .then(function(data) {
                                if (data.success) {
                                    alert('✅ SUCCESS! Bookmark saved!');
                                } else {
                                    alert('❌ ERROR: ' + data.error);
                                }
                            })
                            .catch(function(error) {
                                alert('❌ CONNECTION ERROR: ' + error.message);
                            });
                        } catch (e) {
                            alert('❌ SCRIPT ERROR: ' + e.message);
                        }
                    })();`;

                    document.getElementById('bookmarkletCode').value = bookmarkletCode;
                    document.getElementById('result').innerHTML = '<p style="color: green;">✅ Bookmarklet generated successfully!</p>';

                    // Test the bookmarklet by executing it
                    eval(bookmarkletCode.replace('javascript:', ''));

                } catch (error) {
                    document.getElementById('result').innerHTML = '<p style="color: red;">❌ Error: ' + error.message + '</p>';
                    console.error('Bookmarklet generation error:', error);
                }
            }
        </script>
    </body>
    </html>
    '''

@app.route('/api/test', methods=['GET', 'POST', 'OPTIONS'])
def api_test():
    """Test endpoint to verify API connectivity"""
    if request.method == 'OPTIONS':
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
        response.headers.add('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        return response

    return jsonify({
        'status': 'success',
        'message': 'API is working!',
        'server': request.host,
        'method': request.method,
        'user_agent': request.headers.get('User-Agent', 'Unknown')
    })

if __name__ == '__main__':
    # For HTTPS support (optional)
    import ssl

    # Try to create a simple self-signed certificate context
    try:
        context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        print("HTTPS mode available - use https://*************:5000")
        app.run(debug=True, host='0.0.0.0', port=5000, ssl_context='adhoc')
    except:
        print("HTTPS not available, running HTTP only")
        app.run(debug=True, host='0.0.0.0', port=5000)
