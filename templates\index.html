<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pocket Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }

        .header p {
            color: #666;
            font-size: 0.9rem;
        }

        .controls {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 18px;
            margin-bottom: 20px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 100;
        }

        .search-section {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
        }

        .search-input {
            flex: 1;
            min-width: 300px;
            padding: 8px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 10px 18px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e1e5e9;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
            padding: 6px 12px;
            font-size: 12px;
        }

        .btn-danger:hover {
            transform: translateY(-1px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .search-help {
            font-size: 14px;
            color: #666;
            font-style: italic;
            margin-top: 10px;
        }

        .add-form {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .form-group {
            flex: 1;
            min-width: 200px;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .summary {
            text-align: center;
            margin-bottom: 20px;
            font-size: 16px;
            color: #666;
            font-weight: 500;
        }

        .table-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            max-height: 75vh;
            overflow-y: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed; /* Force consistent column layout */
        }

        /* Ensure all columns are visible */
        table th,
        table td {
            display: table-cell !important;
            visibility: visible !important;
        }

        /* Set column widths to ensure all are visible */
        table th:nth-child(1), table td:nth-child(1) { width: 25%; } /* Title */
        table th:nth-child(2), table td:nth-child(2) { width: 30%; } /* URL */
        table th:nth-child(3), table td:nth-child(3) { width: 20%; } /* Tags */
        table th:nth-child(4), table td:nth-child(4) { width: 12%; } /* Access Count */
        table th:nth-child(5), table td:nth-child(5) { width: 13%; } /* Delete */

        thead {
            position: sticky;
            top: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            z-index: 10;
        }

        thead th {
            padding: 12px;
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-align: left;
            border: none;
        }

        tbody tr {
            transition: all 0.2s ease;
            border-bottom: 1px solid #f1f3f4;
        }

        tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
            cursor: pointer;
        }

        tbody tr.selected {
            background: rgba(102, 126, 234, 0.1);
        }

        td {
            padding: 12px;
            border: none;
            vertical-align: middle;
            font-size: 14px;
        }

        .editable-cell {
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
        }

        .editable-cell:hover {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
        }

        .editable-cell.editing {
            padding: 0;
        }

        .editable-input {
            width: 100%;
            border: 2px solid #667eea;
            border-radius: 6px;
            padding: 10px;
            font-size: 14px;
            font-family: inherit;
            background: #fff;
        }

        .editable-input:focus {
            outline: none;
            border-color: #764ba2;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .access-count {
            text-align: center;
            font-weight: 700;
            font-size: 18px;
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 20px;
            padding: 8px 16px;
            display: inline-block;
            min-width: 40px;
        }

        .url-cell {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            color: #555;
            word-break: break-all;
            max-width: 0; /* Allow text to wrap */
        }

        .url-cell:hover {
            color: #667eea;
        }

        .url-link {
            color: #667eea;
            text-decoration: none;
            display: block;
            padding: 2px 0;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .url-link:hover {
            color: #764ba2;
            background: rgba(102, 126, 234, 0.1);
            text-decoration: underline;
        }

        .url-link:visited {
            color: #8e44ad;
        }

        .edit-hint {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
        }

        /* Dropdown Styles */
        .dropdown {
            position: relative;
            display: inline-block;
            z-index: 10000;
        }

        .dropdown-toggle {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e1e5e9;
            cursor: pointer;
        }

        .dropdown-toggle:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            min-width: 200px;
            overflow: hidden;
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            display: block;
            padding: 12px 20px;
            color: #333;
            text-decoration: none;
            transition: all 0.2s ease;
            border-bottom: 1px solid #f1f3f4;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        /* Modal Styles for Duplicate Results */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f3f4;
        }

        .modal-title {
            font-size: 24px;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #999;
            transition: color 0.2s ease;
        }

        .close:hover {
            color: #333;
        }

        .duplicate-group {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }

        .duplicate-url {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 10px;
            word-break: break-all;
        }

        .duplicate-items {
            margin-left: 20px;
        }

        .duplicate-item {
            background: white;
            padding: 10px 15px;
            margin: 5px 0;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
        }

        .duplicate-item-title {
            font-weight: 600;
            color: #333;
        }

        .duplicate-item-tags {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }

        /* Quick Search Styles */
        .quick-search-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .quick-search-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .quick-search-header h3 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        .quick-search-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }

        .quick-search-btn {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            padding: 10px 8px;
            min-height: 40px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
        }

        .quick-search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.2);
            border-color: #667eea;
        }

        .quick-search-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .quick-search-btn.active:hover {
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
        }

        .quick-search-btn.empty {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 2px dashed #dee2e6;
            color: #6c757d;
        }

        .quick-search-btn.empty:hover {
            border-color: #667eea;
            color: #667eea;
        }

        .quick-search-icon {
            font-size: 16px;
            margin-bottom: 4px;
        }

        .quick-search-label {
            font-size: 12px;
            font-weight: 600;
            line-height: 1.2;
            word-break: break-word;
        }

        .quick-search-btn.empty .quick-search-label {
            font-style: italic;
        }

        /* Edit Modal Styles */
        .edit-modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .edit-modal-content {
            background: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .edit-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        .edit-item {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #e1e5e9;
        }

        .edit-item h4 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #333;
        }

        .edit-input {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 14px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .edit-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .edit-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        /* Responsive Design for Quick Search */
        @media (max-width: 768px) {
            .quick-search-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }

            .quick-search-btn {
                min-height: 35px;
                padding: 8px 5px;
            }

            .quick-search-icon {
                font-size: 14px;
                margin-bottom: 2px;
            }

            .quick-search-label {
                font-size: 10px;
            }

            .edit-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .search-section {
                flex-direction: column;
                align-items: stretch;
            }

            .search-input {
                min-width: auto;
            }

            .action-buttons {
                justify-content: center;
            }

            .form-row {
                flex-direction: column;
            }

            .table-container {
                border-radius: 15px;
            }

            thead th,
            td {
                padding: 10px 8px;
                font-size: 12px;
            }
        }

        /* Scrollbar Styling */
        .table-container::-webkit-scrollbar {
            width: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Pocket Viewer</h1>
            <p>Your personal bookmark collection, beautifully organized</p>
        </div>

        <!-- Controls -->
        <div class="controls">
            <div class="search-section">
                <form method="GET" action="{{ url_for('index') }}" style="display: contents;">
                    <input type="text" name="search" class="search-input"
                           placeholder="Search with AND, OR, NOT operators (e.g., 'python and tutorial', 'linux not kali')..."
                           value="{{ search }}">
                    <button type="submit" class="btn btn-primary">Search</button>
                </form>

                <div class="action-buttons">
                    <a href="{{ url_for('index') }}" class="btn btn-secondary">Clear Filters</a>
                    <button type="button" class="btn btn-primary" onclick="document.querySelector('.add-form').style.display='block'">Add URL</button>

                    <!-- Tools Dropdown -->
                    <div class="dropdown">
                        <button type="button" class="btn btn-secondary dropdown-toggle" onclick="toggleDropdown()">
                            Tools ▼
                        </button>
                        <div class="dropdown-menu" id="toolsDropdown">
                            <a href="#" class="dropdown-item" onclick="checkDuplicates()">Check for Duplicate URLs</a>
                            <a href="#" class="dropdown-item" onclick="validateUrls()">Validate URLs</a>
                            <a href="#" class="dropdown-item" onclick="showBookmarklet()">Browser Bookmarklet</a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="search-help">
                <strong>Search Operators:</strong><br>
                <span style="color: #667eea;">OR:</span> python or javascript (either term) |
                <span style="color: #667eea;">AND:</span> linus and kali (both terms) |
                <span style="color: #667eea;">NOT:</span> linux not kali (exclude term) |
                <span style="color: #667eea;">Quotes:</span> "exact phrase"
            </div>
        </div>

        <!-- Quick Search Buttons -->
        <div class="quick-search-section">
            <div class="quick-search-header">
                <h3>Quick Searches</h3>
                <button type="button" class="btn btn-secondary btn-small" onclick="editQuickSearches()">Edit</button>
            </div>
            <div class="quick-search-grid" id="quickSearchGrid">
                <!-- Quick search buttons will be populated here -->
            </div>
        </div>

        <!-- Add URL Form -->
        <div class="add-form">
            <form method="POST" action="{{ url_for('add') }}">
                <div class="form-row">
                    <div class="form-group">
                        <input type="text" name="title" class="form-input" placeholder="Title" required>
                    </div>
                    <div class="form-group">
                        <input type="url" name="url" class="form-input" placeholder="URL" required>
                    </div>
                    <div class="form-group">
                        <input type="text" name="tags" class="form-input" placeholder="Tags (comma-separated)">
                    </div>
                </div>
                <div class="action-buttons">
                    <button type="submit" class="btn btn-primary">Save</button>
                    <button type="button" class="btn btn-secondary" onclick="document.querySelector('.add-form').style.display='none'">Cancel</button>
                </div>
            </form>
        </div>

        <!-- Summary -->
        <div class="summary">
            Showing {{ items|length }} item{{ 's' if items|length != 1 else '' }}{% if search %} (filtered){% endif %}
        </div>

        <!-- Table -->
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Title <span class="edit-hint">(double-click to edit)</span></th>
                        <th>URL <span class="edit-hint">(double-click to edit)</span></th>
                        <th>Tags <span class="edit-hint">(double-click to edit)</span></th>
                        <th>Access Count</th>
                        <th>Delete</th>
                    </tr>
                </thead>
                <tbody>
                    {% if items %}
                        {% for item in items %}
                        <tr data-url="{{ item.url }}">
                            <td class="editable-cell" data-field="title" data-url="{{ item.url }}" title="Double-click to edit">{{ item.title }}</td>
                            <td class="editable-cell url-cell" data-field="url" data-url="{{ item.url }}" title="Double-click to edit">
                                <a href="{{ item.url }}" target="_blank" class="url-link" onclick="event.stopPropagation(); trackUrlAccess('{{ item.url }}')">{{ item.url }}</a>
                            </td>
                            <td class="editable-cell" data-field="tags" data-url="{{ item.url }}" title="Double-click to edit">{{ item.tags }}</td>
                            <td><span class="access-count">{{ item.access_count or 0 }}</span></td>
                            <td>
                                <form method="POST" action="{{ url_for('delete') }}" onsubmit="return confirm('Delete this item?')" style="display: inline;">
                                    <input type="hidden" name="title" value="{{ item.title }}">
                                    <input type="hidden" name="url" value="{{ item.url }}">
                                    {% if search %}
                                        <input type="hidden" name="search" value="{{ search }}">
                                    {% endif %}
                                    {% if selected_tag %}
                                        <input type="hidden" name="tag" value="{{ selected_tag }}">
                                    {% endif %}
                                    <button type="submit" class="btn btn-danger">Delete</button>
                                </form>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 25px; color: #666; font-style: italic; font-size: 14px;">
                                {% if search %}
                                    No items found matching your search criteria.
                                {% else %}
                                    No bookmarks found. Add some bookmarks to get started!
                                {% endif %}
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Duplicate URLs Modal -->
    <div id="duplicatesModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Duplicate URLs Found</h2>
                <span class="close" onclick="closeDuplicatesModal()">&times;</span>
            </div>
            <div id="duplicatesContent">
                <!-- Duplicate results will be populated here -->
            </div>
        </div>
    </div>

    <!-- URL Validation Modal -->
    <div id="urlValidationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">URL Validation Results</h2>
                <span class="close" onclick="closeUrlValidationModal()">&times;</span>
            </div>
            <div id="urlValidationContent">
                <!-- Validation results will be populated here -->
            </div>
        </div>
    </div>

    <!-- Bookmarklet Modal -->
    <div id="bookmarkletModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h2 class="modal-title">🔖 Browser Bookmarklet Setup</h2>
                <span class="close" onclick="closeBookmarkletModal()">&times;</span>
            </div>
            <div id="bookmarkletContent">
                <div style="padding: 20px;">
                    <div style="margin-bottom: 25px;">
                        <h3 style="color: #333; margin-bottom: 15px;">📌 What is a Bookmarklet?</h3>
                        <p style="color: #666; line-height: 1.6;">
                            A bookmarklet is a special bookmark that runs JavaScript to send the current webpage
                            directly to your Pocket bookmark manager with just one click. It works in any browser
                            (Chrome, Edge, Firefox, Safari, etc.).
                        </p>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <h3 style="color: #333; margin-bottom: 15px;">🚀 Setup Instructions</h3>
                        <ol style="color: #666; line-height: 1.8; padding-left: 20px;">
                            <li><strong>Copy the bookmarklet code</strong> below (click the copy button)</li>
                            <li><strong>Create a new bookmark</strong> in your browser (Ctrl+D or Cmd+D)</li>
                            <li><strong>Name it</strong> something like "Save to Pocket" or "📌 Pocket"</li>
                            <li><strong>Paste the code</strong> as the URL/Location</li>
                            <li><strong>Save the bookmark</strong> to your bookmarks bar for easy access</li>
                        </ol>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <h3 style="color: #333; margin-bottom: 15px;">📋 Bookmarklet Code</h3>
                        <div style="background: #f8f9fa; border: 2px solid #e9ecef; border-radius: 8px; padding: 15px; position: relative;">
                            <code id="bookmarkletCode" style="font-family: 'Courier New', monospace; font-size: 12px; color: #495057; word-break: break-all; display: block; line-height: 1.4;">
                                <!-- Bookmarklet code will be generated here -->
                            </code>
                            <button onclick="copyBookmarkletCode()" style="position: absolute; top: 10px; right: 10px; background: #667eea; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                                📋 Copy
                            </button>
                        </div>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <h3 style="color: #333; margin-bottom: 15px;">✨ How to Use</h3>
                        <ol style="color: #666; line-height: 1.8; padding-left: 20px;">
                            <li><strong>Browse to any webpage</strong> you want to save</li>
                            <li><strong>Click your "Save to Pocket" bookmark</strong> in the bookmarks bar</li>
                            <li><strong>The page will be automatically saved</strong> to your Pocket collection</li>
                            <li><strong>You'll see a confirmation</strong> that the bookmark was added</li>
                        </ol>
                    </div>

                    <div style="background: #e8f4fd; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px; margin-bottom: 20px;">
                        <h4 style="color: #0c5460; margin: 0 0 10px 0;">💡 Pro Tips</h4>
                        <ul style="color: #0c5460; margin: 0; padding-left: 20px; line-height: 1.6;">
                            <li>Add the bookmark to your <strong>bookmarks bar</strong> for quick access</li>
                            <li>Use a short name like <strong>"📌 Pocket"</strong> to save space</li>
                            <li>Works on <strong>any website</strong> - news, articles, tutorials, etc.</li>
                            <li>Automatically captures the <strong>page title and URL</strong></li>
                        </ul>
                    </div>

                    <div style="text-align: center;">
                        <button type="button" class="btn btn-primary" onclick="closeBookmarkletModal()" style="margin-right: 10px;">
                            Got it!
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="testConnection()" style="background: #17a2b8; border-color: #17a2b8; margin-right: 10px;">
                            🔗 Test Connection
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="testBookmarklet()" style="background: #28a745; border-color: #28a745;">
                            🧪 Test Bookmarklet
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Quick Searches Modal -->
    <div id="editQuickSearchModal" class="edit-modal">
        <div class="edit-modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Edit Quick Search Buttons</h2>
                <span class="close" onclick="closeEditQuickSearchModal()">&times;</span>
            </div>
            <div class="edit-grid" id="editQuickSearchGrid">
                <!-- Edit form will be populated here -->
            </div>
            <div class="edit-actions">
                <button type="button" class="btn btn-secondary" onclick="closeEditQuickSearchModal()">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveQuickSearches()">Save Changes</button>
            </div>
        </div>
    </div>

    <script>
        let lastSelected;
        let currentlyEditing = null;
        let doubleClickTimer = null;

        function initializeRowEvents() {
            document.querySelectorAll('tbody tr').forEach(row => {
                row.addEventListener('click', function (e) {
                    // Don't open URL if currently editing
                    if (currentlyEditing) {
                        return;
                    }

                    // Don't open URL if clicking on buttons or form elements
                    if (e.target.tagName === 'BUTTON' || e.target.tagName === 'INPUT' ||
                        e.target.closest('form') || e.target.closest('button')) {
                        return;
                    }

                    // For editable cells, delay the URL opening to check for double-click
                    if (e.target.classList.contains('editable-cell')) {
                        if (doubleClickTimer) {
                            // This is the second click of a double-click, cancel URL opening
                            clearTimeout(doubleClickTimer);
                            doubleClickTimer = null;
                            return;
                        }

                        // Set a timer to open URL after a short delay
                        doubleClickTimer = setTimeout(() => {
                            doubleClickTimer = null;
                            const url = row.getAttribute('data-url');
                            if (url) {
                                trackUrlAccess(url);
                                window.open(url, '_blank');
                            }
                        }, 250); // 250ms delay
                        return;
                    }

                    // For non-editable areas, open URL immediately
                    const url = row.getAttribute('data-url');
                    if (url) {
                        trackUrlAccess(url);
                        window.open(url, '_blank');
                    }
                });

                row.addEventListener('dblclick', function (e) {
                    // Clear any pending URL opening
                    if (doubleClickTimer) {
                        clearTimeout(doubleClickTimer);
                        doubleClickTimer = null;
                    }

                    // Handle double-click on editable cells
                    const editableCell = e.target.classList.contains('editable-cell') ?
                        e.target : e.target.closest('.editable-cell');

                    if (editableCell) {
                        e.preventDefault(); // Prevent link navigation on double-click
                        if (currentlyEditing && currentlyEditing !== editableCell) {
                            cancelEdit(currentlyEditing);
                        }
                        startEdit(editableCell);
                        return;
                    }

                    // Handle double-click on non-editable areas (row selection)
                    if (!currentlyEditing) {
                        if (lastSelected) lastSelected.classList.remove('selected');
                        row.classList.add('selected');
                        lastSelected = row;
                    }
                });
            });
        }

        // Inline editing functions
        function startEdit(cell) {
            if (currentlyEditing) return;

            currentlyEditing = cell;
            const originalValue = cell.textContent.trim();

            // Create input element
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'editable-input';
            input.value = originalValue;

            // Replace cell content with input
            cell.innerHTML = '';
            cell.appendChild(input);
            cell.classList.add('editing');

            // Focus and select text
            input.focus();
            input.select();

            let saveInProgress = false;

            // Handle save on Enter
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    if (!saveInProgress) {
                        saveInProgress = true;
                        saveEdit(cell, input.value, originalValue);
                    }
                } else if (e.key === 'Escape') {
                    cancelEdit(cell, originalValue);
                }
            });

            // Handle save on blur (clicking outside)
            input.addEventListener('blur', function() {
                if (!saveInProgress) {
                    saveInProgress = true;
                    saveEdit(cell, input.value, originalValue);
                }
            });
        }

        function cancelEdit(cell, originalValue = null) {
            if (originalValue === null) {
                originalValue = cell.querySelector('.editable-input')?.value || '';
            }

            const field = cell.getAttribute('data-field');

            if (field === 'url') {
                // For URL fields, restore the clickable link
                cell.innerHTML = `<a href="${originalValue}" target="_blank" class="url-link" onclick="event.stopPropagation(); trackUrlAccess('${originalValue}')">${originalValue}</a>`;
            } else {
                // For other fields, just set the text
                cell.innerHTML = originalValue;
            }

            cell.classList.remove('editing');
            currentlyEditing = null;
        }

        async function saveEdit(cell, newValue, originalValue) {
            const url = cell.getAttribute('data-url');
            const field = cell.getAttribute('data-field');

            console.log(`DEBUG: saveEdit called - field: ${field}, url: ${url}, newValue: ${newValue}`);

            // If value hasn't changed, just cancel edit
            if (newValue === originalValue) {
                cancelEdit(cell, originalValue);
                return;
            }

            try {
                const response = await fetch('/api/update-item', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: url,
                        field: field,
                        value: newValue
                    })
                });

                const result = await response.json();
                console.log(`DEBUG: API response - success: ${result.success}, status: ${response.status}`);

                if (result.success) {
                    // Update cell with new value
                    if (field === 'url') {
                        // For URL fields, create a clickable link
                        cell.innerHTML = `<a href="${newValue}" target="_blank" class="url-link" onclick="event.stopPropagation(); trackUrlAccess('${newValue}')">${newValue}</a>`;
                    } else {
                        // For other fields, just set the text
                        cell.innerHTML = newValue;
                    }
                    cell.classList.remove('editing');
                    currentlyEditing = null;

                    // Special handling for URL field updates
                    if (field === 'url') {
                        // Update the row's data-url attribute
                        const row = cell.closest('tr');
                        row.setAttribute('data-url', newValue);

                        // Update all cells in this row to use the new URL
                        const editableCells = row.querySelectorAll('.editable-cell');
                        editableCells.forEach(editableCell => {
                            editableCell.setAttribute('data-url', newValue);
                        });

                        // Update the delete form's hidden URL input
                        const deleteForm = row.querySelector('form');
                        if (deleteForm) {
                            const urlInput = deleteForm.querySelector('input[name="url"]');
                            if (urlInput) {
                                urlInput.value = newValue;
                            }
                        }
                    }

                    // Show brief success indication
                    cell.style.background = 'rgba(102, 126, 234, 0.2)';
                    setTimeout(() => {
                        cell.style.background = '';
                    }, 1000);
                } else {
                    alert('Error updating: ' + result.error);
                    cancelEdit(cell, originalValue);
                }
            } catch (error) {
                console.error('Error saving edit:', error);
                alert('Error saving changes');
                cancelEdit(cell, originalValue);
            }
        }

        // Track URL access
        async function trackUrlAccess(url) {
            try {
                await fetch('/api/access-url', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ url: url })
                });

                // Update the access count in the UI
                const row = document.querySelector(`tr[data-url="${url}"]`);
                if (row) {
                    const accessCountSpan = row.querySelector('.access-count');
                    if (accessCountSpan) {
                        const currentCount = parseInt(accessCountSpan.textContent) || 0;
                        accessCountSpan.textContent = currentCount + 1;
                    }
                }
            } catch (error) {
                console.error('Error tracking URL access:', error);
            }
        }

        // Dropdown functionality
        function toggleDropdown() {
            const dropdown = document.getElementById('toolsDropdown');
            dropdown.classList.toggle('show');
        }

        // Close dropdown when clicking outside
        window.addEventListener('click', function(e) {
            if (!e.target.matches('.dropdown-toggle')) {
                const dropdown = document.getElementById('toolsDropdown');
                if (dropdown.classList.contains('show')) {
                    dropdown.classList.remove('show');
                }
            }
        });

        // Check for duplicate URLs
        async function checkDuplicates() {
            // Close dropdown
            document.getElementById('toolsDropdown').classList.remove('show');

            try {
                const response = await fetch('/api/check-duplicates');
                const data = await response.json();

                if (data.success) {
                    displayDuplicates(data);
                } else {
                    alert('Error checking duplicates: ' + data.error);
                }
            } catch (error) {
                console.error('Error checking duplicates:', error);
                alert('Error checking duplicates: ' + error.message);
            }
        }

        // Display duplicate results in modal
        function displayDuplicates(data) {
            const modal = document.getElementById('duplicatesModal');
            const content = document.getElementById('duplicatesContent');

            if (data.duplicates.length === 0) {
                content.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <h3>🎉 No Duplicate URLs Found!</h3>
                        <p>Your bookmark collection is clean and organized.</p>
                    </div>
                `;
            } else {
                let html = `
                    <div style="margin-bottom: 20px; padding: 15px; background: #e3f2fd; border-radius: 10px;">
                        <strong>Summary:</strong> Found ${data.total_duplicates} duplicate URL groups with ${data.total_duplicate_items} total items.
                    </div>
                `;

                data.duplicates.forEach(duplicate => {
                    html += `
                        <div class="duplicate-group">
                            <div class="duplicate-url">
                                🔗 ${duplicate.url} (${duplicate.count} copies)
                            </div>
                            <div class="duplicate-items">
                    `;

                    duplicate.items.forEach((item, index) => {
                        html += `
                            <div class="duplicate-item">
                                <div class="duplicate-item-title">${index + 1}. ${item.title || 'Untitled'}</div>
                                ${item.tags ? `<div class="duplicate-item-tags">Tags: ${item.tags}</div>` : ''}
                            </div>
                        `;
                    });

                    html += `
                            </div>
                        </div>
                    `;
                });

                content.innerHTML = html;
            }

            modal.style.display = 'block';
        }

        // Close duplicates modal
        function closeDuplicatesModal() {
            document.getElementById('duplicatesModal').style.display = 'none';
        }

        // URL Validation functionality
        async function validateUrls() {
            // Close dropdown
            document.getElementById('toolsDropdown').classList.remove('show');

            const modal = document.getElementById('urlValidationModal');
            const content = document.getElementById('urlValidationContent');

            // Show loading state with animated progress bar
            content.innerHTML = `
                <div style="text-align: center; padding: 40px;">
                    <div style="font-size: 18px; margin-bottom: 20px;">🔍 Validating URLs...</div>
                    <div style="color: #666; margin-bottom: 10px;">This may take a few minutes depending on the number of URLs.</div>
                    <div style="color: #888; font-size: 14px; margin-bottom: 20px;" id="progressText">Starting validation...</div>
                    <div style="margin-top: 20px;">
                        <div style="width: 100%; background: #f0f0f0; border-radius: 10px; overflow: hidden; position: relative;">
                            <div style="width: 0%; height: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); transition: width 0.5s ease;" id="validationProgress"></div>
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; font-weight: bold;" id="progressPercent">0%</div>
                        </div>
                    </div>
                    <div style="margin-top: 15px; color: #888; font-size: 12px;">
                        <div>⏱️ Estimated time: 2-5 minutes for large collections</div>
                        <div>🔗 Checking for 404 errors, connection failures, and timeouts</div>
                    </div>
                </div>
            `;
            modal.style.display = 'block';

            // Simulate progress bar animation
            let progress = 0;
            const progressBar = document.getElementById('validationProgress');
            const progressPercent = document.getElementById('progressPercent');
            const progressText = document.getElementById('progressText');

            const progressInterval = setInterval(() => {
                progress += Math.random() * 3; // Random increment between 0-3%
                if (progress > 95) progress = 95; // Don't go to 100% until actually done

                progressBar.style.width = progress + '%';
                progressPercent.textContent = Math.round(progress) + '%';

                if (progress < 25) {
                    progressText.textContent = 'Initializing critical URL validation...';
                } else if (progress < 50) {
                    progressText.textContent = 'Checking for connection failures...';
                } else if (progress < 75) {
                    progressText.textContent = 'Testing for 404 errors and timeouts...';
                } else {
                    progressText.textContent = 'Finalizing critical issue report...';
                }
            }, 200);

            try {
                const response = await fetch('/api/validate-urls', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                // Clear progress interval and set to 100%
                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                progressPercent.textContent = '100%';

                if (data.success) {
                    if (data.problems_found === 0) {
                        // No problems found
                        content.innerHTML = `
                            <div style="text-align: center; padding: 40px;">
                                <div style="font-size: 48px; margin-bottom: 20px;">✅</div>
                                <h3 style="color: #28a745; margin-bottom: 15px;">No critical URL issues found!</h3>
                                <p style="color: #666; font-size: 16px;">
                                    Checked ${data.total_checked} URLs - no 404 errors, connection failures, or timeouts detected.
                                </p>
                                <p style="color: #888; font-size: 14px;">
                                    Note: Only critical issues are reported. Other HTTP errors may exist but are not shown.
                                </p>
                                <button type="button" class="btn btn-primary" onclick="closeUrlValidationModal()" style="margin-top: 20px;">
                                    Close
                                </button>
                            </div>
                        `;
                    } else {
                        // Problems found - show them with options
                        let problemsHtml = `
                            <div style="margin-bottom: 20px;">
                                <h3 style="color: #dc3545;">Found ${data.problems_found} critical URL issues</h3>
                                <p style="color: #666;">Checked ${data.total_checked} total URLs. Showing only: <strong>404 errors</strong>, <strong>connection failures</strong>, and <strong>timeouts</strong>.</p>
                                <p style="color: #888; font-size: 14px;">Other HTTP errors (403, 401, 5xx) are ignored as they may be temporary or access-related.</p>
                            </div>
                            <div style="max-height: 400px; overflow-y: auto;">
                                <table style="width: 100%; border-collapse: collapse;">
                                    <thead>
                                        <tr style="background: #f8f9fa;">
                                            <th style="padding: 10px; text-align: left; border-bottom: 2px solid #dee2e6;">
                                                <input type="checkbox" id="selectAllInvalid" onchange="toggleAllInvalidUrls(this)"> Select All
                                            </th>
                                            <th style="padding: 10px; text-align: left; border-bottom: 2px solid #dee2e6;">Title</th>
                                            <th style="padding: 10px; text-align: left; border-bottom: 2px solid #dee2e6;">URL</th>
                                            <th style="padding: 10px; text-align: left; border-bottom: 2px solid #dee2e6;">Error</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                        `;

                        data.problems.forEach((problem, index) => {
                            problemsHtml += `
                                <tr style="border-bottom: 1px solid #dee2e6;">
                                    <td style="padding: 10px;">
                                        <input type="checkbox" class="invalid-url-checkbox" value="${problem.url}" id="invalid_${index}">
                                    </td>
                                    <td style="padding: 10px; max-width: 200px; word-break: break-word;">
                                        ${problem.title}
                                    </td>
                                    <td style="padding: 10px; max-width: 300px; word-break: break-all; font-family: monospace; font-size: 12px;">
                                        <a href="${problem.url}" target="_blank" style="color: #667eea; text-decoration: none;"
                                           onmouseover="this.style.textDecoration='underline'"
                                           onmouseout="this.style.textDecoration='none'"
                                           title="Click to test this URL manually">${problem.url}</a>
                                    </td>
                                    <td style="padding: 10px; color: #dc3545;">
                                        ${problem.error}
                                    </td>
                                </tr>
                            `;
                        });

                        problemsHtml += `
                                    </tbody>
                                </table>
                            </div>
                            <div style="margin-top: 20px; text-align: center; border-top: 1px solid #dee2e6; padding-top: 20px;">
                                <button type="button" class="btn btn-danger" onclick="deleteSelectedInvalidUrls()" style="margin-right: 10px;">
                                    Delete Selected URLs
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="closeUrlValidationModal()">
                                    Keep All & Close
                                </button>
                            </div>
                        `;

                        content.innerHTML = problemsHtml;
                    }
                } else {
                    clearInterval(progressInterval);
                    content.innerHTML = `
                        <div style="text-align: center; padding: 40px;">
                            <div style="font-size: 48px; margin-bottom: 20px;">❌</div>
                            <h3 style="color: #dc3545; margin-bottom: 15px;">Validation Error</h3>
                            <p style="color: #666;">${data.error}</p>
                            <button type="button" class="btn btn-primary" onclick="closeUrlValidationModal()" style="margin-top: 20px;">
                                Close
                            </button>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error validating URLs:', error);
                clearInterval(progressInterval);
                content.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <div style="font-size: 48px; margin-bottom: 20px;">❌</div>
                        <h3 style="color: #dc3545; margin-bottom: 15px;">Connection Error</h3>
                        <p style="color: #666;">Failed to validate URLs. Please try again.</p>
                        <button type="button" class="btn btn-primary" onclick="closeUrlValidationModal()" style="margin-top: 20px;">
                            Close
                        </button>
                    </div>
                `;
            }
        }

        // Close URL validation modal
        function closeUrlValidationModal() {
            document.getElementById('urlValidationModal').style.display = 'none';
        }

        // Toggle all invalid URL checkboxes
        function toggleAllInvalidUrls(selectAllCheckbox) {
            const checkboxes = document.querySelectorAll('.invalid-url-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        }

        // Delete selected invalid URLs
        async function deleteSelectedInvalidUrls() {
            const checkboxes = document.querySelectorAll('.invalid-url-checkbox:checked');
            const urlsToDelete = Array.from(checkboxes).map(cb => cb.value);

            if (urlsToDelete.length === 0) {
                alert('Please select at least one URL to delete.');
                return;
            }

            if (!confirm(`Are you sure you want to delete ${urlsToDelete.length} invalid URL(s)? This action cannot be undone.`)) {
                return;
            }

            try {
                const response = await fetch('/api/delete-invalid-urls', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        urls: urlsToDelete
                    })
                });

                const data = await response.json();

                if (data.success) {
                    alert(`Successfully deleted ${data.deleted_count} invalid URL(s).`);
                    closeUrlValidationModal();
                    // Refresh the page to show updated list
                    window.location.reload();
                } else {
                    alert('Error deleting URLs: ' + data.error);
                }
            } catch (error) {
                console.error('Error deleting invalid URLs:', error);
                alert('Error deleting URLs. Please try again.');
            }
        }

        // Bookmarklet functionality
        function showBookmarklet() {
            // Close dropdown
            document.getElementById('toolsDropdown').classList.remove('show');

            const modal = document.getElementById('bookmarkletModal');
            const codeElement = document.getElementById('bookmarkletCode');

            // Generate bookmarklet code - ensure proper URL formatting
            const serverUrl = window.location.origin.replace(/\\\\/g, '/');
            console.log('Generated bookmarklet for server:', serverUrl); // Debug log
            const bookmarkletCode = `javascript:(function(){
                try {
                    var title = document.title || document.URL;
                    var url = document.URL;
                    var serverUrl = '${serverUrl}'.replace(/\\\\\\\\/g, '/');

                    // Immediate feedback to show bookmarklet is running
                    alert('🚀 Bookmarklet started!\\n\\nTitle: ' + title + '\\nURL: ' + url + '\\nServer: ' + serverUrl + '\\n\\nClick OK to continue...');

                    console.log('Bookmarklet starting - Title:', title, 'URL:', url, 'Server:', serverUrl);

                    fetch(serverUrl + '/api/add-bookmark', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            title: title,
                            url: url
                        })
                    })
                    .then(function(response) {
                        console.log('Response received - Status:', response.status, 'OK:', response.ok);
                        return response.text().then(function(text) {
                            console.log('Raw response text:', text);
                            try {
                                var data = JSON.parse(text);
                                return { status: response.status, data: data, ok: response.ok };
                            } catch (e) {
                                console.error('JSON parse error:', e);
                                return { status: response.status, data: { error: 'Invalid JSON response: ' + text }, ok: response.ok };
                            }
                        });
                    })
                    .then(function(result) {
                        console.log('Processed result:', result);
                        if (result.ok && result.data.success) {
                            alert('✅ SUCCESS! Bookmark saved!\\n\\nTitle: ' + title + '\\nURL: ' + url + '\\nServer: ' + serverUrl);
                        } else if (result.status === 409) {
                            alert('⚠️ DUPLICATE! This URL is already bookmarked!\\n\\nExisting title: ' + (result.data.existing_title || 'Unknown') + '\\nURL: ' + url);
                        } else {
                            alert('❌ ERROR! Failed to save bookmark\\n\\nStatus: ' + result.status + '\\nError: ' + (result.data.error || 'Unknown error') + '\\nServer: ' + serverUrl);
                        }
                    })
                    .catch(function(error) {
                        console.error('Fetch error:', error);
                        alert('❌ CONNECTION ERROR!\\n\\nServer: ' + serverUrl + '\\nError: ' + error.message + '\\n\\nCheck console for details');
                    });
                } catch (e) {
                    console.error('Bookmarklet script error:', e);
                    alert('❌ SCRIPT ERROR!\\n\\nError: ' + e.message + '\\n\\nCheck console for details');
                }
            })();`;

            codeElement.textContent = bookmarkletCode;
            modal.style.display = 'block';
        }

        function closeBookmarkletModal() {
            document.getElementById('bookmarkletModal').style.display = 'none';
        }

        function copyBookmarkletCode() {
            const codeElement = document.getElementById('bookmarkletCode');
            const textArea = document.createElement('textarea');
            textArea.value = codeElement.textContent;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);

            // Show feedback
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = '✅ Copied!';
            button.style.background = '#28a745';

            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = '#667eea';
            }, 2000);
        }

        function testConnection() {
            // Test basic API connectivity
            const serverUrl = window.location.origin;

            fetch('/api/test', {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert('✅ Connection successful!\n\nServer: ' + data.server + '\nAPI is working correctly.\n\nYou can now test the bookmarklet or use it on external websites.');
                } else {
                    alert('❌ API test failed: ' + JSON.stringify(data));
                }
            })
            .catch(error => {
                alert('❌ Connection test failed: ' + error.message + '\nServer: ' + serverUrl + '\n\nPlease ensure the Pocket app is running and accessible.');
            });
        }

        function testBookmarklet() {
            // First test basic connectivity
            const serverUrl = window.location.origin;

            fetch('/api/test', {
                method: 'GET'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // If basic connectivity works, test the actual bookmark saving
                    testActualBookmarkSave();
                } else {
                    alert('❌ API test failed: ' + JSON.stringify(data));
                }
            })
            .catch(error => {
                alert('❌ Connection test failed: ' + error.message + '\nServer: ' + serverUrl);
            });
        }

        function testActualBookmarkSave() {
            // Test the bookmarklet with the current page
            const title = document.title + ' (Test)';
            const url = window.location.href;

            fetch('/api/add-bookmark', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    title: title + ' (Test)',
                    url: url + '#test-bookmark'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('✅ Test successful! Bookmark added:\\n\\nTitle: ' + data.bookmark.title + '\\nURL: ' + data.bookmark.url);
                } else {
                    alert('❌ Test failed: ' + data.error);
                }
            })
            .catch(error => {
                alert('❌ Test failed: ' + error.message);
            });
        }

        // Close modal when clicking outside
        window.addEventListener('click', function(e) {
            const duplicatesModal = document.getElementById('duplicatesModal');
            const urlValidationModal = document.getElementById('urlValidationModal');
            const bookmarkletModal = document.getElementById('bookmarkletModal');

            if (e.target === duplicatesModal) {
                closeDuplicatesModal();
            }
            if (e.target === urlValidationModal) {
                closeUrlValidationModal();
            }
            if (e.target === bookmarkletModal) {
                closeBookmarkletModal();
            }
        });

        // Quick Search functionality
        let quickSearches = [];

        // Load quick searches on page load
        async function loadQuickSearches() {
            try {
                const response = await fetch('/api/quick-searches');
                const data = await response.json();

                if (data.success) {
                    quickSearches = data.quick_searches;
                    renderQuickSearchButtons();
                }
            } catch (error) {
                console.error('Error loading quick searches:', error);
                // Initialize with empty buttons
                quickSearches = Array(8).fill().map(() => ({label: '', search: ''}));
                renderQuickSearchButtons();
            }
        }

        // Render quick search buttons
        function renderQuickSearchButtons() {
            const grid = document.getElementById('quickSearchGrid');
            grid.innerHTML = '';

            quickSearches.forEach((item, index) => {
                const button = document.createElement('div');
                button.className = `quick-search-btn ${item.search ? 'active' : 'empty'}`;
                button.onclick = () => performQuickSearch(item.search);

                if (item.search) {
                    button.innerHTML = `
                        <div class="quick-search-icon">🔍</div>
                        <div class="quick-search-label">${item.label || item.search}</div>
                    `;
                } else {
                    button.innerHTML = `
                        <div class="quick-search-icon">➕</div>
                        <div class="quick-search-label">Empty Slot</div>
                    `;
                }

                grid.appendChild(button);
            });
        }

        // Perform quick search
        function performQuickSearch(searchTerm) {
            if (searchTerm) {
                const searchInput = document.querySelector('input[name="search"]');
                searchInput.value = searchTerm;
                searchInput.form.submit();
            }
        }

        // Edit quick searches
        function editQuickSearches() {
            const modal = document.getElementById('editQuickSearchModal');
            const grid = document.getElementById('editQuickSearchGrid');

            grid.innerHTML = '';

            quickSearches.forEach((item, index) => {
                const editItem = document.createElement('div');
                editItem.className = 'edit-item';
                editItem.innerHTML = `
                    <h4>Button ${index + 1}</h4>
                    <input type="text" class="edit-input" placeholder="Display Label"
                           value="${item.label}" data-index="${index}" data-field="label">
                    <input type="text" class="edit-input" placeholder="Search Term"
                           value="${item.search}" data-index="${index}" data-field="search">
                `;
                grid.appendChild(editItem);
            });

            modal.style.display = 'block';
        }

        // Save quick searches
        async function saveQuickSearches() {
            const inputs = document.querySelectorAll('#editQuickSearchGrid .edit-input');
            const newQuickSearches = Array(8).fill().map(() => ({label: '', search: ''}));

            inputs.forEach(input => {
                const index = parseInt(input.dataset.index);
                const field = input.dataset.field;
                newQuickSearches[index][field] = input.value.trim();
            });

            try {
                const response = await fetch('/api/quick-searches', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        quick_searches: newQuickSearches
                    })
                });

                const data = await response.json();

                if (data.success) {
                    quickSearches = newQuickSearches;
                    renderQuickSearchButtons();
                    closeEditQuickSearchModal();
                } else {
                    alert('Error saving quick searches: ' + data.error);
                }
            } catch (error) {
                console.error('Error saving quick searches:', error);
                alert('Error saving quick searches: ' + error.message);
            }
        }

        // Close edit modal
        function closeEditQuickSearchModal() {
            document.getElementById('editQuickSearchModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.addEventListener('click', function(e) {
            const editModal = document.getElementById('editQuickSearchModal');
            if (e.target === editModal) {
                closeEditQuickSearchModal();
            }
        });

        // Initialize on page load
        initializeRowEvents();
        loadQuickSearches();
    </script>
</body>
</html>
