<!DOCTYPE html>
<html>
<head>
    <title>JavaScript Test</title>
</head>
<body>
    <h1>JavaScript Syntax Test</h1>
    <button onclick="testBookmarklet()">Test Bookmarklet Generation</button>
    <div id="result"></div>

    <script>
        function testBookmarklet() {
            try {
                // Simulate the problematic URL
                const serverUrl = 'http://127.0.0.1:5000';
                const testUrl = "https://knews.kathimerini.com.cy/en/news/eu-sets-new-rules-for-carry-on-bags-starting-september-1#:~:text=Here's%20a%20summary%20of%20the,seat%20in%20front%20of%20you.";
                
                // Test the bookmarklet generation (simplified version)
                const bookmarkletCode = `javascript:(function(){
                    var title = document.title || document.URL;
                    var url = document.URL;
                    var serverUrl = '${serverUrl}';
                    alert('Test successful! URL: ' + url);
                })();`;
                
                // Test trackUrlAccess with problematic URL (using tojson equivalent)
                const jsonUrl = JSON.stringify(testUrl);
                console.log('JSON escaped URL:', jsonUrl);
                
                // Test CSS.escape
                const escapedUrl = CSS.escape(testUrl);
                console.log('CSS escaped URL:', escapedUrl);
                
                document.getElementById('result').innerHTML = `
                    <h3>✅ JavaScript syntax test passed!</h3>
                    <p><strong>Original URL:</strong> ${testUrl}</p>
                    <p><strong>JSON escaped:</strong> ${jsonUrl}</p>
                    <p><strong>CSS escaped:</strong> ${escapedUrl}</p>
                    <p><strong>Bookmarklet generated successfully</strong></p>
                `;
                
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3>❌ JavaScript syntax error:</h3>
                    <p>${error.message}</p>
                `;
                console.error('Test failed:', error);
            }
        }
    </script>
</body>
</html>
