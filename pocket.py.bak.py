from flask import Flask, render_template, request, redirect, url_for, jsonify
import pandas as pd
import os
import sys
import shutil
import math
from datetime import datetime

app = Flask(__name__)

# --------- PyInstaller Compatibility ---------
def resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.abspath("."), relative_path)

BUNDLED_DATA_FILE = resource_path('data.xlsx')
APP_FOLDER = os.path.dirname(os.path.abspath(sys.executable if getattr(sys, 'frozen', False) else __file__))
DATA_FILE = os.path.join(APP_FOLDER, 'data.xlsx')

if not os.path.exists(DATA_FILE):
    shutil.copy(BUNDLED_DATA_FILE, DATA_FILE)

# --------- Helpers ---------
def load_data():
    if os.path.exists(DATA_FILE):
        df = pd.read_excel(DATA_FILE)
        df.fillna('', inplace=True)
        return df.to_dict(orient='records')
    return []

def save_data(data):
    df = pd.DataFrame(data)
    df.to_excel(DATA_FILE, index=False)

# --------- Routes ---------
@app.route('/')
def index():
    data = load_data()
    search = request.args.get('search', '').lower()
    tag_filter = request.args.get('tag', '').lower()

    # 🔍 Search and tag filter BEFORE pagination
    if search:
        data = [item for item in data if search in str(item['title']).lower()
                                      or search in str(item['url']).lower()
                                      or search in str(item.get('tags', '')).lower()]
    if tag_filter:
        data = [item for item in data if tag_filter in str(item.get('tags', '')).lower()]

    page = int(request.args.get('page', 1))
    per_page = 10
    total_items = len(data)
    total_pages = max(1, math.ceil(total_items / per_page))
    data_paginated = data[(page - 1) * per_page: page * per_page]

    return render_template('index.html', items=data_paginated, search=search,
                           selected_tag=tag_filter, page=page, total_pages=total_pages)

@app.route('/upload', methods=['POST'])
def upload():
    file = request.files['file']
    if file:
        file.save(DATA_FILE)
    return redirect(url_for('index'))

@app.route('/add', methods=['POST'])
def add():
    data = load_data()
    new_item = {
        'title': request.form['title'],
        'url': request.form['url'],
        'tags': request.form.get('tags', ''),
        'time_added': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'status': 'unread'
    }
    data.append(new_item)
    save_data(data)
    return redirect(url_for('index'))

@app.route('/delete', methods=['POST'])
def delete():
    title = request.form['title']
    url = request.form['url']
    data = load_data()
    data = [item for item in data if not (item['title'] == title and item['url'] == url)]
    save_data(data)
    return redirect(url_for('index'))

@app.route('/api/items')
def api_items():
    data = load_data()
    return jsonify(data)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
